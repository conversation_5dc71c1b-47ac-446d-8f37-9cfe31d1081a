<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title id="dynamic-title">Login</title>

    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Sora:wght@100;200;300;400;500;600;700;800&display=swap"
      rel="stylesheet"
    />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    />

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Sora", sans-serif;
      }

      body {
        min-height: 100vh;
        background-color: #fff;
      }

      p {
        font-size: 16px;
      }

      .container {
        display: flex;
        min-height: calc(100vh - 100px);
        width: 100%;
        justify-content: space-between;
        align-items: center;
        flex-direction: column;
      }

      .main-container {
        padding: 20px;
        display: flex;
        height: 100%;
      }

      .left-section {
        width: 55%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        padding: 0 30px;
        background-color: white;
      }

      .banner {
        background: url("https://presencedevstorage.blob.core.windows.net/files/login-banner.png")
          center/cover no-repeat;
        min-height: calc(100vh - 200px);
        border-radius: 20px;
      }

      .overlay {
        position: relative;
      }

      .content-area {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: start;
        padding: 4rem 3rem;
      }

      .logo-area {
        display: flex;
        justify-content: center;
        gap: 12px;
        height: 60px; /* Prevents layout shift when logos are added */
        align-items: center;
      }

      .hero-content {
        margin-top: auto;
        color: white;
      }

      .hero-content h2 {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .hero-content p {
        font-size: 16px;
        line-height: 1.5;
      }

      .right-section {
        width: 45%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        padding: 20px;
        background-color: white;
      }

      .login-container {
        width: 100%;
        max-width: 60%;
        margin-top: 20px;
      }

      .login-text {
        text-align: center;
        margin-top: 36px;
        margin-bottom: 30px;
      }

      .login-text h2 {
        font-size: 28px;
        color: #333;
        margin-bottom: 10px;
      }

      .login-text p {
        color: #666;
      }

      .form-group {
        margin-bottom: 20px;
        position: relative;
      }

      .form-group input {
        width: 100%;
        padding: 12px 12px 12px 40px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 16px;
        background-color: #fff;
      }

      .form-group input:focus {
        outline: none;
        border-color: #e31837;
      }

      .input-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
      }

      .input-icon img {
        width: 20px;
        height: 20px;
      }

      .sign-in-btn {
        width: 100%;
        padding: 14px;
        background-color: #e31837;
        color: #fff;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        margin-bottom: 20px;
        transition: background-color 0.3s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }

      .sign-in-btn:hover {
        background-color: #c41530;
      }

      .sign-in-btn:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }

      .spinner {
        width: 16px;
        height: 16px;
        border: 2px solid #ffffff;
        border-top: 2px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        display: none;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error-message {
        background-color: #fee;
        color: #c53030;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 20px;
        border: 1px solid #feb2b2;
        display: none;
        font-size: 14px;
        text-align: center;
      }

      .error-message.show {
        display: block;
      }

      .forgot-password {
        display: block;
        text-align: center;
        font-size: 14px;
        color: #0066cc;
        text-decoration: none;
      }

      .forgot-password:hover {
        text-decoration: underline;
      }

      .partner-logos {
        display: flex;
        justify-content: center;
        gap: 30px;
        flex-wrap: wrap;
        width: 100%;
        margin-bottom: 50px;
        margin-top: 50px;
      }

      .partner-logo {
        width: 150px;
      }

      .footer-container {
        padding: 0px 50px;
        width: 100%;
      }

      .footer-line {
        height: 1px;
        background: linear-gradient(
          to right,
          transparent 0%,
          #0000004d 5%,
          #0000004d 95%,
          transparent 100%
        );
      }

      .footer-content {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        padding: 16px 40px;
      }

      .footer-nav {
        display: flex;
        gap: 20px;
      }

      .footer-links {
        color: #757575;
        font-size: 12px;
        display: block;
        text-align: center;
        text-decoration: none;
        white-space: nowrap;
      }

      /* Tablet */
      @media screen and (max-width: 1024px) {
        .container {
          padding: 15px;
        }

        .left-section,
        .right-section {
          width: 50%;
        }

        .login-container {
          max-width: 80%;
        }

        .hero-content h2 {
          font-size: 28px;
        }

        .hero-content p {
          font-size: 14px;
        }

        .partner-logo {
          width: 150px;
        }
      }

      /* Mobile */
      @media screen and (max-width: 768px) {
        .main-container {
          flex-direction: column-reverse;
          padding: 10px;
        }

        .left-section,
        .right-section {
          width: 100%;
          padding: 20px;
        }

        .content-area {
          padding: 1.5rem;
        }

        .banner {
          min-height: 500px;
        }

        .login-container {
          max-width: 100%;
          padding: 0 20px;
          margin-top: 15px;
        }

        .hero-content h2 {
          font-size: 20px;
          margin-bottom: 12px;
        }

        .hero-content p {
          font-size: 12px;
        }

        .partner-logo {
          width: 150px;
        }

        .footer-container {
          padding: 0px 20px;
          width: 100%;
        }

        .footer-content {
          display: flex;
          gap: 20px;
          padding: 16px 10px;
        }

        .footer-nav {
          display: flex;
          gap: 20px;
          align-items: center;
          flex-wrap: wrap;
        }
      }

      /* Small Mobile */
      @media screen and (max-width: 480px) {
        .container {
          padding: 5px;
        }

        .left-section,
        .right-section {
          padding: 15px;
        }

        .banner {
          min-height: 400px;
        }

        .login-container {
          padding: 0 15px;
        }

        .hero-content h2 {
          font-size: 16px;
        }

        .hero-content p {
          font-size: 12px;
        }

        .form-group input {
          padding-left: 32px;
          font-size: 13px;
        }

        .input-icon img {
          width: 14px;
          height: 14px;
        }

        .sign-in-btn {
          font-size: 13px;
          padding: 10px;
        }

        .partner-logo {
          width: 150px;
          margin: 5px;
        }

        .forgot-password {
          font-size: 12px;
        }
      }
    </style>
  </head>

  <body>
    <div class="container">
      <div class="main-container">
        <!-- Left Banner Section -->
        <div class="left-section">
          <div class="banner overlay content-area">
            <div class="hero-content">
              <h2 data-key="heroTitle">
                Keep businesses connected through technology
              </h2>
              <p data-key="heroText">
                Aramark's ability to identify, attract, develop, and support the
                right relationships with suppliers, vendors, and service
                providers is key to our mutual growth.
              </p>
            </div>
          </div>
        </div>

        <!-- Right Login Section -->
        <div class="right-section">
          <div class="login-container">
            <div class="logo-area" id="logo-area"></div>
            <div class="login-text">
              <h2 data-key="welcome">Welcome Back</h2>
              <p data-key="loginInstruction">Please login to continue</p>
              <div id="error-message" class="error-message"></div>
            </div>
            <form class="login-form" onsubmit="return false;" method="post">
              <div class="form-group">
                <span class="input-icon">
                  <img
                    src="https://presencedevstorage.blob.core.windows.net/files/user.svg"
                    alt="User Icon"
                  />
                </span>
                <input
                  id="username"
                  type="text"
                  placeholder="Username"
                  required
                  data-key-placeholder="username"
                />
              </div>
              <div class="form-group">
                <span class="input-icon">
                  <img
                    src="https://presencedevstorage.blob.core.windows.net/files/password.svg"
                    alt="Password Icon"
                  />
                </span>
                <input
                  id="password"
                  type="password"
                  placeholder="Password"
                  required
                  data-key-placeholder="password"
                />
              </div>
              <button
                type="submit"
                class="sign-in-btn"
                id="btn-login"
                data-key="signIn"
              >
                <div class="spinner" id="login-spinner"></div>
                <span id="btn-text">SIGN IN</span>
              </button>
              <a href="#" class="forgot-password" data-key="forgot"
                >Forgot your password?</a
              >
            </form>
          </div>

          <div class="partner-logos">
            <img
              src="https://presencedevstorage.blob.core.windows.net/files/complete.png"
              class="partner-logo"
              alt="Complete"
            />
            <img
              src="https://presencedevstorage.blob.core.windows.net/files/gespra.png"
              class="partner-logo"
              alt="Gespra"
            />
            <img
              src="https://presencedevstorage.blob.core.windows.net/files/quasep.png"
              class="partner-logo"
              alt="Quasep"
            />
          </div>
        </div>
      </div>
      <div class="footer-container">
        <div class="footer-line"></div>
        <div class="footer-content">
          <div>
            <span class="footer-links" id="copyright-year">© Copyright </span>
          </div>
          <div class="footer-nav">
            <a href="#" class="footer-links" data-key="about-aramark"
              >About Aramark</a
            >
            <a href="#" class="footer-links" data-key="terms-and-conditions"
              >Terms & Conditions</a
            >
            <a href="#" class="footer-links" data-key="privacy-policy"
              >Privacy Policy</a
            >
          </div>
        </div>
      </div>
    </div>
    <div class="captcha-container"></div>
    <!-- Language Translation Script -->
    <script>
      const translations = {
        fr: {
          welcome: "Bon retour",
          loginInstruction: "Veuillez vous connecter pour continuer",
          username: "Nom d'utilisateur",
          password: "Mot de passe",
          signIn: "SE CONNECTER",
          forgot: "Mot de passe oublié ?",
          heroTitle:
            "Maintenir les entreprises connectées grâce à la technologie",
          heroText:
            "La capacité d'Aramark à identifier, attirer, développer et entretenir les bonnes relations avec les fournisseurs, les vendeurs et les prestataires de services est essentielle à notre croissance mutuelle.",
        },
      };

      const userLang = navigator.language || navigator.userLanguage;

      if (userLang.startsWith("fr")) {
        const dict = translations.fr;

        document.querySelectorAll("[data-key]").forEach((el) => {
          const key = el.getAttribute("data-key");
          if (dict[key]) el.innerText = dict[key];
        });

        document.querySelectorAll("[data-key-placeholder]").forEach((el) => {
          const key = el.getAttribute("data-key-placeholder");
          if (dict[key]) el.setAttribute("placeholder", dict[key]);
        });
      }
    </script>

    <script src="https://cdn.auth0.com/js/auth0/9.28/auth0.min.js"></script>
    <script src="https://cdn.auth0.com/js/polyfills/1.0/object-assign.min.js"></script>
    <script>
      window.addEventListener("load", function () {
        let config = JSON.parse(
          decodeURIComponent(escape(window.atob("@@config@@")))
        );

        let leeway = config.internalOptions.leeway;
        if (leeway) {
          let convertedLeeway = parseInt(leeway);

          if (!isNaN(convertedLeeway)) {
            config.internalOptions.leeway = convertedLeeway;
          }
        }

        let params = {
          overrides: {
            __tenant: config.auth0Tenant,
            __token_issuer: config.authorizationServer.issuer,
          },
          domain: config.auth0Domain,
          clientID: config.clientID,
          redirectUri: config.callbackURL,
          responseType: "code",
          scope: config.internalOptions.scope,
          _csrf: config.internalOptions._csrf,
          state: config.internalOptions.state,
          _intstate: config.internalOptions._intstate,
        };

        let triggerCaptcha = null;
        let signupCaptcha = null;
        let webAuth = new auth0.WebAuth(params);
        let databaseConnection = "AD-arahlthcare";
        let captcha = webAuth.renderCaptcha(
          document.querySelector(".captcha-container"),
          null,
          (error, payload) => {
            if (payload) {
              triggerCaptcha = payload.triggerCaptcha;
            }
          }
        );

        function login(e) {
          e.preventDefault();
          let button = document.getElementById("btn-login");
          let spinner = document.getElementById("login-spinner");
          let btnText = document.getElementById("btn-text");
          let username = document.getElementById("username").value;
          let password = document.getElementById("password").value;

          // Show spinner and disable button
          button.disabled = true;
          spinner.style.display = "block";
          btnText.textContent = "Signing in...";
          button.style.backgroundColor = "#c41530";

          // Hide any previous error messages
          hideError();

          let request = () => {
            webAuth.login(
              {
                realm: databaseConnection,
                username: username,
                password: password,
                captcha: captcha.getValue(),
              },
              function (err) {
                if (err) {
                  displayError(err);
                }
                // Reset button state
                button.disabled = false;
                spinner.style.display = "none";
                btnText.textContent = "SIGN IN";
                button.style.backgroundColor = "#e31837";
              }
            );
          };

          if (triggerCaptcha) {
            triggerCaptcha(request);
          } else {
            request();
          }
        }

        function displayError(err) {
          captcha.reload();
          let errorMessage = document.getElementById("error-message");
          let errorText =
            err.policy ||
            err.description ||
            "An error occurred during login. Please try again.";
          errorMessage.innerText = errorText;
          errorMessage.classList.add("show");

          // Auto-hide error after 5 seconds
          setTimeout(hideError, 5000);
        }

        function hideError() {
          let errorMessage = document.getElementById("error-message");
          errorMessage.classList.remove("show");
        }

        document.getElementById("btn-login").addEventListener("click", login);
      });
    </script>

    <script>
      // Get audience from query params and determine appType
      function getAppTypeFromAudience() {
        const params = new URLSearchParams(window.location.search);
        const audience = (params.get("audience") || "").toLowerCase();
        if (audience.includes("supplier")) return "supplier";
        return "admin";
      }
      let appType = getAppTypeFromAudience();

      // Branding assets for each app type
      let branding = {
        supplier: {
          title: "Supplier Portal - Login",
          logo: "https://presencedevstorage.blob.core.windows.net/files/logo.png",
          logoText:
            "https://presencedevstorage.blob.core.windows.net/files/logo-text.png",
        },
        admin: {
          title: "Admin Portal - Login",
          logo: "https://presencedevstorage.blob.core.windows.net/files/ecps-logo.png",
          logoText:
            "https://presencedevstorage.blob.core.windows.net/files/ecps-logo-text.png",
        },
      };

      // Apply branding based on appType
      document.addEventListener("DOMContentLoaded", function () {
        let b = branding[appType] || branding.supplier;
        document.title = b.title;
        let titleEl = document.getElementById("dynamic-title");
        if (titleEl) titleEl.textContent = b.title;
        // Dynamically append logo images
        let logoArea = document.getElementById("logo-area");
        if (logoArea) {
          logoArea.innerHTML = "";
          let logoImg = document.createElement("img");
          logoImg.id = "logo-img";
          logoImg.src = b.logo;
          logoImg.alt = "Logo";
          logoImg.width = 58;
          logoArea.appendChild(logoImg);
          let logoTextImg = document.createElement("img");
          logoTextImg.id = "logo-text-img";
          logoTextImg.src = b.logoText;
          logoTextImg.alt = "Logo Text";
          logoTextImg.width = 198;
          logoArea.appendChild(logoTextImg);
        }
      });
    </script>

    <script>
      // Set current year in footer
      document.addEventListener("DOMContentLoaded", function () {
        var yearSpan = document.getElementById("copyright-year");
        if (yearSpan) {
          var year = new Date().getFullYear();
          yearSpan.textContent = `© Copyright ${year}`;
        }
      });
    </script>
  </body>
</html>
