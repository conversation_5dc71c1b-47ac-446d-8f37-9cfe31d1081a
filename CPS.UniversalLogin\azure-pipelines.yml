trigger:
  branches:
    include:
      - develop

pool:
  name: 'CPSAPP'

variables:
  NodeVersion: '20.x'

stages:
- stage: DeployDev
  displayName: 'Deploy to Auth0 Dev'
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/develop')
  jobs:
  - job: Deploy
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(NodeVersion)'
      displayName: 'Use Node.js'

    - task: AzureKeyVault@2
      inputs:
        azureSubscription: 'AzureDevops-Pipelines'
        KeyVaultName: 'ar-az-est1-d-cps-kv-001'
        SecretsFilter: 'auth0-dev-secret'
        RunAsPreJob: true

    - script: npm install -g auth0-deploy-cli
      displayName: 'Install Auth0 Deploy CLI'

    - powershell: |
        # Prepare Auth0 deployment folder
        New-Item -ItemType Directory -Force -Path "deploy\pages"
        Copy-Item "branding\index.html" "deploy\pages\login.html"

        # Auth0 requires JSON descriptor
        @"
        {
          "enabled": true,
          "name": "login",
          "html": "login.html"
        }
        "@ | Out-File -FilePath "deploy\pages\login.json" -Encoding UTF8

        # Create tenant.yaml for a0deploy to read
        @"
        pages:
          - name: login
            html: ./pages/login.html
            enabled: true
        "@ | Out-File -FilePath "deploy\tenant.yaml" -Encoding UTF8

        # Run deployment (credentials provided via env block)
        a0deploy import --input_file deploy --debug
      displayName: 'Deploy Login Page to Auth0 Dev'
      env:
        AUTH0_DOMAIN: cpsdev.us.auth0.com
        AUTH0_CLIENT_ID: N2QYkh1eNzsseBg0sA88DS3ScMR5Mojl
        AUTH0_CLIENT_SECRET: $(auth0-dev-secret)

- stage: DeployQA
  displayName: 'Deploy to Auth0 QA'
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/qa')
  jobs:
  - job: Deploy
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '$(NodeVersion)'
      displayName: 'Use Node.js'

    - task: AzureKeyVault@2
      inputs:
        azureSubscription: 'AzureDevops-Pipelines'
        KeyVaultName: 'ar-az-est1-q-cps-kv-001'
        SecretsFilter: 'auth0-qa-secret'
        RunAsPreJob: true

    - script: npm install -g auth0-deploy-cli
      displayName: 'Install Auth0 Deploy CLI'

    - script: |
        # Prepare Auth0 deployment folder
        mkdir -p deploy/pages
        cp branding/index.html deploy/pages/login.html

        # Auth0 requires JSON descriptor
        cat <<EOF > deploy/pages/login.json
        {
          "enabled": true,
          "name": "login",
          "html": "login.html"
        }
        EOF

        # Create tenant.yaml for a0deploy to read
        cat <<EOF > deploy/tenant.yaml
        pages:
          - name: login
            html: ./pages/login.html
            enabled: true
        EOF

        # Run deployment (credentials provided via env block)
        a0deploy import --input_file deploy --debug
      displayName: 'Deploy Login Page to Auth0 Dev'
      env:
        AUTH0_DOMAIN: cpsdev.us.auth0.com
        AUTH0_CLIENT_ID: N2QYkh1eNzsseBg0sA88DS3ScMR5Mojl
        AUTH0_CLIENT_SECRET: $(auth0-qa-secret)