# CPS.UniversalLogin

A customizable Auth0 Universal Login HTML template for Aramark and partner applications. This template provides a modern, responsive login experience with dynamic branding and localization support, and can be used for any number of applications (supplier, admin, or others) by configuring the branding logic.

## Features

- Dynamic branding for multiple apps (logos, titles, hero text, etc.)
- Language translation (English and French)
- Responsive design for desktop and mobile
- Auth0 integration with captcha support
- Easy configuration via URL query parameters

## Getting Started

1. **Clone or Download** this repository.
2. **Customize** as needed:
   - To use for any application, set the `audience` query parameter in the Auth0 Universal Login URL. The template will select branding based on the value of `audience` (e.g., if it contains `supplier`, `admin`, or any other keyword you add to the branding object in `index.html`).
   - Update logo images, hero text, and add new app branding in the `branding` object in `index.html` as needed.
3. **Deploy** the `index.html` to your Auth0 Universal Login page as a custom template.

## Configuration

- **Branding**: Controlled by the `audience` query parameter. Logos, titles, and hero text are set dynamically. You can add more app types to the `branding` object.
- **Localization**: French translations are included. Add more languages in the `translations` object if needed.

## Build and Test

No build step is required. To test:

- Open `index.html` in a browser and append `?audience=your-app-name` to the URL to preview branding for different apps.
- For Auth0 integration, deploy to your Auth0 tenant and test login flows.

---
For more on customizing Auth0 Universal Login, see the [Auth0 documentation](https://auth0.com/docs/customize/universal-login-pages/universal-login-page-templates).